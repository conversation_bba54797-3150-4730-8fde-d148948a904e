<script setup lang="ts">
  import { computed, ref, watch } from 'vue';

  import { PlusOutlined } from '@ant-design/icons-vue';

  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher/index';

  interface Props {
    disabled?: boolean;
    modelValue?: string;
    placeholder?: string;
  }

  interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'change', value: string, option: any): void;
    (e: 'addSubject'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
    modelValue: '',
    placeholder: '请选择对方会计科目',
  });

  const emit = defineEmits<Emits>();

  // 使用会计科目 hooks
  const {
    error,
    loading,
    pureSubjectOptions, // 使用纯科目选项，不包含辅助核算
    refreshAccountSubjects,
  } = useAccountSubjects();

  const selectedValue = ref(props.modelValue);

  // 处理科目选项，如果当前值不在选项中则添加为默认选项
  const enhancedSubjectOptions = computed(() => {
    const options = [...pureSubjectOptions.value];

    // 检查当前值是否在选项中，如果不在则添加为默认选项
    if (props.modelValue && props.modelValue !== '') {
      const currentValueExists = options.some(
        (option) => option.value === props.modelValue
      );

      if (!currentValueExists) {
        console.log('⚠️ 当前科目值在选项中不存在，添加为默认选项:', props.modelValue);
        // 添加当前值作为默认选项，显示原始内容
        options.push({
          value: props.modelValue,
          label: props.modelValue, // 直接显示原始值
          code: props.modelValue,
          name: props.modelValue,
          useAssistant: false,
          assistantType: '',
          assistantOptions: [],
        });
      }
    }

    console.log('✅ 增强后的科目选项:', {
      totalCount: options.length,
      currentValueInOptions: props.modelValue ? options.some(opt => opt.value === props.modelValue) : false,
      originalOptionsCount: pureSubjectOptions.value.length,
    });

    return options;
  });

  // 搜索过滤函数
  const filterOption = (input: string, option: any) => {
    const searchText = input.toLowerCase();
    return (
      option.label.toLowerCase().includes(searchText) ||
      option.code.toLowerCase().includes(searchText) ||
      option.name.toLowerCase().includes(searchText)
    );
  };

  // 选择变化处理
  const handleChange = (value: any) => {
    const stringValue = value ? String(value) : '';
    selectedValue.value = stringValue;
    emit('update:modelValue', stringValue);
  };

  // 新增科目
  const handleAddSubject = () => {
    emit('addSubject');
  };

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      selectedValue.value = newValue;
    },
  );

  // 暴露方法给父组件
  defineExpose({
    refreshAccountSubjects,
  });
</script>

<template>
  <div class="counterpart-account-selector">
    <a-select
      v-model:value="selectedValue"
      :placeholder="placeholder"
      show-search
      allow-clear
      :disabled="disabled"
      :loading="loading"
      :options="enhancedSubjectOptions"
      :filter-option="filterOption"
      style="width: 100%"
      @change="handleChange"
    >
      <template #notFoundContent>
        <div v-if="loading">
          <a-spin size="small" />
          加载中...
        </div>
        <div v-else-if="error">
          {{ error }}
        </div>
        <div v-else>暂无数据</div>
      </template>

      <!-- 下拉框底部的新增科目按钮 -->
      <template #dropdownRender="{ menuNode }">
        <div>
          <component :is="menuNode" />
          <a-divider style="margin: 4px 0" />
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 4px 8px;
              color: #1890ff;
              cursor: pointer;
            "
            @click="handleAddSubject"
          >
            <PlusOutlined style="margin-right: 4px" />
            新增科目
          </div>
        </div>
      </template>
    </a-select>
  </div>
</template>

<style lang="scss" scoped>
  .counterpart-account-selector {
    width: 100%;
  }
</style>
